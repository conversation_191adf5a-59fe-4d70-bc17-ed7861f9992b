"""
数据库功能测试
"""
import asyncio
import os
import pytest
from app.core.database import init_database
from app.services.database_service import DatabaseService
from app.models.database_models import SystemConfig, ApiKey, TradingParam, TradingPair, AiConfig, RiskParam


class TestDatabase:
    """数据库测试类"""
    
    def __init__(self):
        self.test_db_path = "test_trading_system.db"
        self.db_service = DatabaseService(self.test_db_path)
    
    async def setup(self):
        """测试前准备"""
        # 删除测试数据库文件（如果存在）
        if os.path.exists(self.test_db_path):
            os.remove(self.test_db_path)
        
        # 临时修改数据库路径进行测试
        original_db_path = "trading_system.db"
        
        # 初始化测试数据库
        import app.core.database
        app.core.database.init_database.__globals__['db_path'] = self.test_db_path
        await init_database()
    
    async def cleanup(self):
        """测试后清理"""
        try:
            if os.path.exists(self.test_db_path):
                os.remove(self.test_db_path)
        except PermissionError:
            # 文件被占用时忽略删除错误
            pass
    
    async def test_system_config(self):
        """测试系统配置功能"""
        print("测试系统配置功能...")
        
        # 测试获取默认配置
        config = await self.db_service.get_system_config("app_name")
        assert config is not None
        assert config.value == "DeepSeek量化交易系统"
        print("✓ 获取默认配置成功")
        
        # 测试设置新配置
        success = await self.db_service.set_system_config("test_key", "test_value", "测试配置")
        assert success == True
        
        # 验证新配置
        new_config = await self.db_service.get_system_config("test_key")
        assert new_config is not None
        assert new_config.value == "test_value"
        print("✓ 设置新配置成功")
        
        # 测试获取所有配置
        all_configs = await self.db_service.get_all_system_configs()
        assert len(all_configs) >= 4  # 至少有4个默认配置
        print("✓ 获取所有配置成功")
    
    async def test_api_key(self):
        """测试API密钥功能"""
        print("测试API密钥功能...")
        
        # 创建测试API密钥
        api_key = ApiKey(
            exchange="okx",
            api_key="test_api_key",
            secret_key="test_secret_key",
            passphrase="test_passphrase",
            sandbox=True
        )
        
        # 保存API密钥
        success = await self.db_service.save_api_key(api_key)
        assert success == True
        print("✓ 保存API密钥成功")
        
        # 获取API密钥
        saved_key = await self.db_service.get_api_key("okx")
        assert saved_key is not None
        assert saved_key.api_key == "test_api_key"
        assert saved_key.sandbox == True
        print("✓ 获取API密钥成功")
    
    async def test_trading_param(self):
        """测试交易参数功能"""
        print("测试交易参数功能...")
        
        # 测试获取默认参数
        leverage_param = await self.db_service.get_trading_param("default_leverage")
        assert leverage_param is not None
        assert leverage_param.param_value == "10"
        print("✓ 获取默认交易参数成功")
        
        # 创建新交易参数
        new_param = TradingParam(
            param_name="test_param",
            param_value="100",
            param_type="integer",
            description="测试参数",
            min_value=1,
            max_value=1000
        )
        
        # 保存参数
        success = await self.db_service.set_trading_param(new_param)
        assert success == True
        
        # 验证参数
        saved_param = await self.db_service.get_trading_param("test_param")
        assert saved_param is not None
        assert saved_param.param_value == "100"
        print("✓ 设置交易参数成功")
        
        # 获取所有参数
        all_params = await self.db_service.get_all_trading_params()
        assert len(all_params) >= 6  # 至少有6个默认参数
        print("✓ 获取所有交易参数成功")
    
    async def test_trading_pair(self):
        """测试交易对功能"""
        print("测试交易对功能...")
        
        # 创建测试交易对
        pair = TradingPair(
            symbol="BTC/USDT",
            base_currency="BTC",
            quote_currency="USDT",
            is_enabled=True,
            min_order_size=0.001,
            max_order_size=100.0,
            price_precision=2,
            amount_precision=6
        )
        
        # 保存交易对
        success = await self.db_service.save_trading_pair(pair)
        assert success == True
        print("✓ 保存交易对成功")
        
        # 获取启用的交易对
        enabled_pairs = await self.db_service.get_enabled_trading_pairs()
        assert len(enabled_pairs) >= 1
        assert enabled_pairs[0].symbol == "BTC/USDT"
        print("✓ 获取启用交易对成功")
    
    async def test_ai_config(self):
        """测试AI配置功能"""
        print("测试AI配置功能...")
        
        # 创建AI配置
        ai_config = AiConfig(
            model_name="deepseek-chat",
            api_key="test_deepseek_key",
            api_url="https://api.deepseek.com/v1/chat/completions",
            model_type="opening",
            prompt_template="测试提示词模板",
            max_tokens=1000,
            temperature=0.7
        )
        
        # 保存配置
        success = await self.db_service.save_ai_config(ai_config)
        assert success == True
        print("✓ 保存AI配置成功")
        
        # 获取配置
        saved_config = await self.db_service.get_ai_config("opening")
        assert saved_config is not None
        assert saved_config.model_name == "deepseek-chat"
        print("✓ 获取AI配置成功")
    
    async def test_risk_param(self):
        """测试风险参数功能"""
        print("测试风险参数功能...")
        
        # 获取默认风险参数
        risk_param = await self.db_service.get_risk_param("max_daily_loss")
        assert risk_param is not None
        assert risk_param.param_value == 0.05
        print("✓ 获取默认风险参数成功")
        
        # 获取所有风险参数
        all_risk_params = await self.db_service.get_all_risk_params()
        assert len(all_risk_params) >= 4  # 至少有4个默认风险参数
        print("✓ 获取所有风险参数成功")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("开始数据库功能测试...")
        
        try:
            await self.setup()
            
            await self.test_system_config()
            await self.test_api_key()
            await self.test_trading_param()
            await self.test_trading_pair()
            await self.test_ai_config()
            await self.test_risk_param()
            
            print("\n🎉 所有数据库测试通过！")
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            raise
        finally:
            await self.cleanup()


async def main():
    """主测试函数"""
    test = TestDatabase()
    await test.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
