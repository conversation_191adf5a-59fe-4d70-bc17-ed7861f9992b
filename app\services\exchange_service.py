"""
交易所服务 - 封装CCXT接口
"""
import ccxt
import asyncio
from typing import Dict, List, Optional, Any
from loguru import logger
from app.models.database_models import ApiKey
from app.services.database_service import db_service


class ExchangeService:
    """交易所服务类"""
    
    def __init__(self):
        self.exchange = None
        self.is_connected = False
        self.exchange_id = "okx"
    
    async def initialize(self, api_key: ApiKey) -> bool:
        """初始化交易所连接"""
        try:
            # 配置交易所参数
            exchange_config = {
                'apiKey': api_key.api_key,
                'secret': api_key.secret_key,
                'password': api_key.passphrase,  # OKX需要passphrase
                'sandbox': api_key.sandbox,  # 模拟盘/实盘切换
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'swap',  # 永续合约
                }
            }
            
            # 创建交易所实例
            self.exchange = ccxt.okx(exchange_config)
            
            # 测试连接
            await self.test_connection()
            
            self.is_connected = True
            logger.info(f"交易所连接成功 - {'模拟盘' if api_key.sandbox else '实盘'}")
            return True
            
        except Exception as e:
            logger.error(f"交易所连接失败: {e}")
            self.is_connected = False
            return False
    
    async def test_connection(self) -> bool:
        """测试交易所连接"""
        try:
            # 简单测试连接 - 获取服务器时间
            server_time = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_time
            )
            return server_time is not None
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False
    
    async def get_balance(self) -> Optional[Dict]:
        """获取账户余额"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取账户余额
            balance = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_balance
            )
            
            logger.info("账户余额获取成功")
            return balance
            
        except Exception as e:
            logger.error(f"获取账户余额失败: {e}")
            return None
    
    async def get_positions(self) -> Optional[List[Dict]]:
        """获取持仓信息"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取持仓信息
            positions = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_positions
            )
            
            # 过滤掉空仓位
            active_positions = [pos for pos in positions if float(pos.get('contracts', 0)) != 0]
            
            logger.info(f"获取持仓信息成功，当前持仓数量: {len(active_positions)}")
            return active_positions
            
        except Exception as e:
            logger.error(f"获取持仓信息失败: {e}")
            return None
    
    async def get_markets(self) -> Optional[Dict]:
        """获取交易市场信息"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取市场信息
            markets = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.load_markets
            )
            
            # 过滤永续合约
            swap_markets = {
                symbol: market for symbol, market in markets.items()
                if market.get('type') == 'swap' and market.get('active', False)
            }
            
            logger.info(f"获取市场信息成功，永续合约数量: {len(swap_markets)}")
            return swap_markets
            
        except Exception as e:
            logger.error(f"获取市场信息失败: {e}")
            return None
    
    async def get_ticker(self, symbol: str) -> Optional[Dict]:
        """获取单个交易对的行情数据"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取行情数据
            ticker = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_ticker, symbol
            )
            
            logger.debug(f"获取 {symbol} 行情数据成功")
            return ticker
            
        except Exception as e:
            logger.error(f"获取 {symbol} 行情数据失败: {e}")
            return None
    
    async def get_ohlcv(self, symbol: str, timeframe: str = '1m', limit: int = 100) -> Optional[List]:
        """获取K线数据"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取K线数据
            ohlcv = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_ohlcv, symbol, timeframe, None, limit
            )
            
            logger.debug(f"获取 {symbol} {timeframe} K线数据成功，数量: {len(ohlcv)}")
            return ohlcv
            
        except Exception as e:
            logger.error(f"获取 {symbol} {timeframe} K线数据失败: {e}")
            return None
    
    async def create_order(self, symbol: str, order_type: str, side: str, 
                          amount: float, price: float = None, params: Dict = None) -> Optional[Dict]:
        """创建订单"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 创建订单参数
            order_params = params or {}
            
            # 创建订单
            order = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.create_order, symbol, order_type, side, amount, price, order_params
            )
            
            logger.info(f"订单创建成功: {symbol} {side} {amount} @ {price}")
            return order
            
        except Exception as e:
            logger.error(f"创建订单失败: {e}")
            return None
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """取消订单"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return False
        
        try:
            # 取消订单
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.cancel_order, order_id, symbol
            )
            
            logger.info(f"订单取消成功: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消订单失败: {e}")
            return False
    
    async def get_order_status(self, order_id: str, symbol: str) -> Optional[Dict]:
        """获取订单状态"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return None
        
        try:
            # 获取订单状态
            order = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.fetch_order, order_id, symbol
            )
            
            logger.debug(f"获取订单状态成功: {order_id}")
            return order
            
        except Exception as e:
            logger.error(f"获取订单状态失败: {e}")
            return None
    
    async def set_leverage(self, symbol: str, leverage: int) -> bool:
        """设置杠杆倍数"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return False
        
        try:
            # 设置杠杆
            result = await asyncio.get_event_loop().run_in_executor(
                None, self.exchange.set_leverage, leverage, symbol
            )
            
            logger.info(f"设置杠杆成功: {symbol} {leverage}x")
            return True
            
        except Exception as e:
            logger.error(f"设置杠杆失败: {e}")
            return False
    
    async def close_position(self, symbol: str, side: str = None) -> bool:
        """平仓"""
        if not self.is_connected or not self.exchange:
            logger.error("交易所未连接")
            return False
        
        try:
            # 获取当前持仓
            positions = await self.get_positions()
            if not positions:
                logger.warning("没有持仓需要平仓")
                return True
            
            # 找到对应的持仓
            target_position = None
            for pos in positions:
                if pos['symbol'] == symbol:
                    if side is None or pos['side'] == side:
                        target_position = pos
                        break
            
            if not target_position:
                logger.warning(f"未找到 {symbol} 的持仓")
                return True
            
            # 平仓
            amount = abs(float(target_position['contracts']))
            close_side = 'sell' if target_position['side'] == 'long' else 'buy'
            
            order = await self.create_order(
                symbol=symbol,
                order_type='market',
                side=close_side,
                amount=amount,
                params={'reduceOnly': True}
            )
            
            if order:
                logger.info(f"平仓成功: {symbol} {target_position['side']} {amount}")
                return True
            else:
                return False
                
        except Exception as e:
            logger.error(f"平仓失败: {e}")
            return False


# 创建全局交易所服务实例
exchange_service = ExchangeService()
