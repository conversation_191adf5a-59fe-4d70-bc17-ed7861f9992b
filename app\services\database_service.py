"""
数据库服务层 - 提供数据访问接口
"""
import aiosqlite
from typing import List, Optional, Dict, Any
from loguru import logger
from app.models.database_models import (
    SystemConfig, ApiKey, TradingParam, TradingPair, AiConfig, RiskParam
)


class DatabaseService:
    """数据库服务类"""
    
    def __init__(self, db_path: str = "trading_system.db"):
        self.db_path = db_path
    
    async def get_connection(self):
        """获取数据库连接"""
        return aiosqlite.connect(self.db_path)
    
    # ==================== 系统配置相关 ====================
    
    async def get_system_config(self, key: str) -> Optional[SystemConfig]:
        """获取系统配置"""
        async with self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM system_config WHERE key = ?", (key,)
            )
            row = await cursor.fetchone()
            if row:
                columns = [desc[0] for desc in cursor.description]
                return SystemConfig(**dict(zip(columns, row)))
            return None
    
    async def set_system_config(self, key: str, value: str, description: str = None) -> bool:
        """设置系统配置"""
        try:
            async with await self.get_connection() as db:
                await db.execute("""
                    INSERT OR REPLACE INTO system_config (key, value, description, updated_at)
                    VALUES (?, ?, ?, CURRENT_TIMESTAMP)
                """, (key, value, description))
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"设置系统配置失败: {e}")
            return False
    
    async def get_all_system_configs(self) -> List[SystemConfig]:
        """获取所有系统配置"""
        async with await self.get_connection() as db:
            cursor = await db.execute("SELECT * FROM system_config ORDER BY key")
            rows = await cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            return [SystemConfig(**dict(zip(columns, row))) for row in rows]
    
    # ==================== API密钥相关 ====================
    
    async def save_api_key(self, api_key: ApiKey) -> bool:
        """保存API密钥"""
        try:
            async with await self.get_connection() as db:
                if api_key.id:
                    # 更新现有记录
                    await db.execute("""
                        UPDATE api_keys SET 
                        exchange=?, api_key=?, secret_key=?, passphrase=?, 
                        sandbox=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """, (api_key.exchange, api_key.api_key, api_key.secret_key,
                          api_key.passphrase, api_key.sandbox, api_key.is_active, api_key.id))
                else:
                    # 插入新记录
                    await db.execute("""
                        INSERT INTO api_keys 
                        (exchange, api_key, secret_key, passphrase, sandbox, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)
                    """, (api_key.exchange, api_key.api_key, api_key.secret_key,
                          api_key.passphrase, api_key.sandbox, api_key.is_active))
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"保存API密钥失败: {e}")
            return False
    
    async def get_api_key(self, exchange: str) -> Optional[ApiKey]:
        """获取指定交易所的API密钥"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM api_keys WHERE exchange = ? AND is_active = 1", (exchange,)
            )
            row = await cursor.fetchone()
            if row:
                columns = [desc[0] for desc in cursor.description]
                return ApiKey(**dict(zip(columns, row)))
            return None
    
    # ==================== 交易参数相关 ====================
    
    async def get_trading_param(self, param_name: str) -> Optional[TradingParam]:
        """获取交易参数"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM trading_params WHERE param_name = ? AND is_active = 1", 
                (param_name,)
            )
            row = await cursor.fetchone()
            if row:
                columns = [desc[0] for desc in cursor.description]
                return TradingParam(**dict(zip(columns, row)))
            return None
    
    async def set_trading_param(self, param: TradingParam) -> bool:
        """设置交易参数"""
        try:
            async with await self.get_connection() as db:
                await db.execute("""
                    INSERT OR REPLACE INTO trading_params 
                    (param_name, param_value, param_type, description, min_value, max_value, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (param.param_name, param.param_value, param.param_type,
                      param.description, param.min_value, param.max_value))
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"设置交易参数失败: {e}")
            return False
    
    async def get_all_trading_params(self) -> List[TradingParam]:
        """获取所有交易参数"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM trading_params WHERE is_active = 1 ORDER BY param_name"
            )
            rows = await cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            return [TradingParam(**dict(zip(columns, row))) for row in rows]
    
    # ==================== 交易对相关 ====================
    
    async def save_trading_pair(self, pair: TradingPair) -> bool:
        """保存交易对配置"""
        try:
            async with await self.get_connection() as db:
                await db.execute("""
                    INSERT OR REPLACE INTO trading_pairs 
                    (symbol, base_currency, quote_currency, is_enabled, 
                     min_order_size, max_order_size, price_precision, amount_precision, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (pair.symbol, pair.base_currency, pair.quote_currency, pair.is_enabled,
                      pair.min_order_size, pair.max_order_size, pair.price_precision, pair.amount_precision))
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"保存交易对配置失败: {e}")
            return False
    
    async def get_enabled_trading_pairs(self) -> List[TradingPair]:
        """获取启用的交易对"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM trading_pairs WHERE is_enabled = 1 ORDER BY symbol"
            )
            rows = await cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            return [TradingPair(**dict(zip(columns, row))) for row in rows]
    
    async def get_all_trading_pairs(self) -> List[TradingPair]:
        """获取所有交易对"""
        async with await self.get_connection() as db:
            cursor = await db.execute("SELECT * FROM trading_pairs ORDER BY symbol")
            rows = await cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            return [TradingPair(**dict(zip(columns, row))) for row in rows]
    
    # ==================== AI配置相关 ====================
    
    async def get_ai_config(self, model_type: str) -> Optional[AiConfig]:
        """获取AI模型配置"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM ai_config WHERE model_type = ? AND is_active = 1", 
                (model_type,)
            )
            row = await cursor.fetchone()
            if row:
                columns = [desc[0] for desc in cursor.description]
                return AiConfig(**dict(zip(columns, row)))
            return None
    
    async def save_ai_config(self, config: AiConfig) -> bool:
        """保存AI配置"""
        try:
            async with await self.get_connection() as db:
                if config.id:
                    await db.execute("""
                        UPDATE ai_config SET 
                        model_name=?, api_key=?, api_url=?, model_type=?, prompt_template=?,
                        max_tokens=?, temperature=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                        WHERE id=?
                    """, (config.model_name, config.api_key, config.api_url, config.model_type,
                          config.prompt_template, config.max_tokens, config.temperature, 
                          config.is_active, config.id))
                else:
                    await db.execute("""
                        INSERT INTO ai_config 
                        (model_name, api_key, api_url, model_type, prompt_template, max_tokens, temperature, is_active)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (config.model_name, config.api_key, config.api_url, config.model_type,
                          config.prompt_template, config.max_tokens, config.temperature, config.is_active))
                await db.commit()
                return True
        except Exception as e:
            logger.error(f"保存AI配置失败: {e}")
            return False
    
    # ==================== 风险参数相关 ====================
    
    async def get_risk_param(self, param_name: str) -> Optional[RiskParam]:
        """获取风险参数"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM risk_params WHERE param_name = ? AND is_active = 1", 
                (param_name,)
            )
            row = await cursor.fetchone()
            if row:
                columns = [desc[0] for desc in cursor.description]
                return RiskParam(**dict(zip(columns, row)))
            return None
    
    async def get_all_risk_params(self) -> List[RiskParam]:
        """获取所有风险参数"""
        async with await self.get_connection() as db:
            cursor = await db.execute(
                "SELECT * FROM risk_params WHERE is_active = 1 ORDER BY param_name"
            )
            rows = await cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            return [RiskParam(**dict(zip(columns, row))) for row in rows]


# 创建全局数据库服务实例
db_service = DatabaseService()
