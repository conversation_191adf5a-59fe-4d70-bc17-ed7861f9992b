"""
数据库初始化和管理
"""
import sqlite3
import aiosqlite
from pathlib import Path
from loguru import logger
from typing import Dict, Any, List, Optional
import json
from datetime import datetime


async def init_database():
    """初始化数据库"""
    db_path = "trading_system.db"

    # 确保数据库文件存在
    if not Path(db_path).exists():
        logger.info("创建新的数据库文件")

    async with aiosqlite.connect(db_path) as db:
        # 创建系统配置表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS system_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建API密钥表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS api_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                exchange TEXT NOT NULL,
                api_key TEXT NOT NULL,
                secret_key TEXT NOT NULL,
                passphrase TEXT,
                sandbox BOOLEAN DEFAULT TRUE,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建交易参数表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS trading_params (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                param_name TEXT UNIQUE NOT NULL,
                param_value TEXT NOT NULL,
                param_type TEXT NOT NULL,
                description TEXT,
                min_value REAL,
                max_value REAL,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建交易对配置表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS trading_pairs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT UNIQUE NOT NULL,
                base_currency TEXT NOT NULL,
                quote_currency TEXT NOT NULL,
                is_enabled BOOLEAN DEFAULT FALSE,
                min_order_size REAL,
                max_order_size REAL,
                price_precision INTEGER,
                amount_precision INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建AI模型配置表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS ai_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                model_name TEXT NOT NULL,
                api_key TEXT NOT NULL,
                api_url TEXT NOT NULL,
                model_type TEXT NOT NULL,  -- 'opening' or 'holding'
                prompt_template TEXT NOT NULL,
                max_tokens INTEGER DEFAULT 1000,
                temperature REAL DEFAULT 0.7,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # 创建风险控制参数表
        await db.execute("""
            CREATE TABLE IF NOT EXISTS risk_params (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                param_name TEXT UNIQUE NOT NULL,
                param_value REAL NOT NULL,
                param_unit TEXT,  -- 'percent', 'absolute', 'ratio'
                description TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

        await db.commit()
        logger.info("数据库表结构创建完成")

        # 插入默认配置数据
        await insert_default_data(db)

        logger.info("数据库初始化完成")


async def insert_default_data(db):
    """插入默认配置数据"""

    # 默认系统配置
    default_configs = [
        ("app_name", "DeepSeek量化交易系统", "应用名称"),
        ("version", "1.0.0", "系统版本"),
        ("debug_mode", "true", "调试模式"),
        ("log_level", "INFO", "日志级别"),
    ]

    for key, value, desc in default_configs:
        await db.execute("""
            INSERT OR IGNORE INTO system_config (key, value, description)
            VALUES (?, ?, ?)
        """, (key, value, desc))

    # 默认交易参数
    default_trading_params = [
        ("default_leverage", "10", "integer", "默认杠杆倍数", 1, 100),
        ("max_position_size", "0.1", "float", "最大仓位比例", 0.01, 1.0),
        ("stop_loss_percent", "0.02", "float", "止损比例", 0.005, 0.1),
        ("take_profit_percent", "0.04", "float", "止盈比例", 0.01, 0.2),
        ("min_confidence", "0.7", "float", "最小置信度", 0.5, 1.0),
        ("data_limit", "100", "integer", "K线数据获取条数", 50, 500),
    ]

    for name, value, ptype, desc, min_val, max_val in default_trading_params:
        await db.execute("""
            INSERT OR IGNORE INTO trading_params
            (param_name, param_value, param_type, description, min_value, max_value)
            VALUES (?, ?, ?, ?, ?, ?)
        """, (name, value, ptype, desc, min_val, max_val))

    # 默认风险控制参数
    default_risk_params = [
        ("max_daily_loss", "0.05", "percent", "每日最大亏损比例"),
        ("max_drawdown", "0.15", "percent", "最大回撤比例"),
        ("position_size_limit", "0.1", "percent", "单笔交易最大仓位"),
        ("leverage_limit", "20", "ratio", "最大杠杆倍数"),
    ]

    for name, value, unit, desc in default_risk_params:
        await db.execute("""
            INSERT OR IGNORE INTO risk_params (param_name, param_value, param_unit, description)
            VALUES (?, ?, ?, ?)
        """, (name, value, unit, desc))

    await db.commit()
    logger.info("默认配置数据插入完成")


async def get_db_connection():
    """获取数据库连接"""
    return await aiosqlite.connect("trading_system.db")
