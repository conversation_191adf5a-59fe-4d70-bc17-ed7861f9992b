"""
交易所接口测试
"""
import asyncio
import os
from dotenv import load_dotenv
from app.services.exchange_service import ExchangeService
from app.models.database_models import ApiKey

# 加载环境变量
load_dotenv()


async def test_exchange_connection():
    """测试交易所连接"""
    print("开始交易所连接测试...")
    
    # 从环境变量获取API密钥（需要用户提供真实密钥）
    api_key = os.getenv('OKX_API_KEY')
    secret_key = os.getenv('OKX_SECRET_KEY')
    passphrase = os.getenv('OKX_PASSPHRASE')
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请设置环境变量: OKX_API_KEY, OKX_SECRET_KEY, OKX_PASSPHRASE")
        print("提示：可以在.env文件中设置这些变量")
        return False
    
    # 创建API密钥对象 - 根据用户说明是模拟盘密钥
    api_key_obj = ApiKey(
        exchange="okx",
        api_key=api_key,
        secret_key=secret_key,
        passphrase=passphrase,
        sandbox=True  # 用户说明是模拟盘密钥
    )
    
    # 创建交易所服务实例
    exchange_service = ExchangeService()
    
    try:
        # 测试连接
        success = await exchange_service.initialize(api_key_obj)
        if not success:
            print("❌ 交易所连接失败")
            return False

        print(f"✓ 交易所连接成功 - {'模拟盘' if api_key_obj.sandbox else '实盘'}")
        
        # 测试获取账户余额
        balance = await exchange_service.get_balance()
        if balance:
            print("✓ 获取账户余额成功")
            print(f"  USDT余额: {balance.get('USDT', {}).get('free', 0)}")
        else:
            print("❌ 获取账户余额失败")
        
        # 测试获取市场信息
        markets = await exchange_service.get_markets()
        if markets:
            print(f"✓ 获取市场信息成功，永续合约数量: {len(markets)}")
            # 显示前5个交易对
            symbols = list(markets.keys())[:5]
            print(f"  示例交易对: {symbols}")
        else:
            print("❌ 获取市场信息失败")
        
        # 测试获取行情数据
        if markets:
            test_symbol = 'BTC/USDT:USDT'  # OKX永续合约格式
            if test_symbol in markets:
                ticker = await exchange_service.get_ticker(test_symbol)
                if ticker:
                    print(f"✓ 获取 {test_symbol} 行情数据成功")
                    print(f"  当前价格: {ticker.get('last', 'N/A')}")
                else:
                    print(f"❌ 获取 {test_symbol} 行情数据失败")
        
        # 测试获取K线数据
        if markets:
            test_symbol = 'BTC/USDT:USDT'
            if test_symbol in markets:
                ohlcv = await exchange_service.get_ohlcv(test_symbol, '1m', 10)
                if ohlcv:
                    print(f"✓ 获取 {test_symbol} K线数据成功，数量: {len(ohlcv)}")
                    if ohlcv:
                        latest = ohlcv[-1]
                        print(f"  最新K线: 开盘{latest[1]}, 最高{latest[2]}, 最低{latest[3]}, 收盘{latest[4]}")
                else:
                    print(f"❌ 获取 {test_symbol} K线数据失败")
        
        # 测试获取持仓信息
        positions = await exchange_service.get_positions()
        if positions is not None:
            print(f"✓ 获取持仓信息成功，当前持仓数量: {len(positions)}")
            if positions:
                for pos in positions[:3]:  # 显示前3个持仓
                    print(f"  持仓: {pos['symbol']} {pos['side']} {pos['contracts']}")
        else:
            print("❌ 获取持仓信息失败")
        
        print("\n🎉 交易所接口测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        return False


async def test_without_api_keys():
    """无API密钥的基础测试"""
    print("开始基础功能测试（无需API密钥）...")
    
    # 测试交易所服务类的基本功能
    exchange_service = ExchangeService()
    
    # 测试未连接状态的方法
    balance = await exchange_service.get_balance()
    assert balance is None, "未连接状态应该返回None"
    print("✓ 未连接状态测试通过")
    
    positions = await exchange_service.get_positions()
    assert positions is None, "未连接状态应该返回None"
    print("✓ 未连接状态测试通过")
    
    print("✓ 基础功能测试完成")


async def main():
    """主测试函数"""
    print("=== 交易所接口测试 ===\n")
    
    # 先进行基础测试
    await test_without_api_keys()
    print()
    
    # 尝试进行完整测试
    await test_exchange_connection()


if __name__ == "__main__":
    asyncio.run(main())
